//
//  ChatView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI

// MARK: - Chat View

struct ChatView: View {
    @State private var messageText: String = ""
    @State private var messages: [ChatMessage] = []
    @State private var isOrbAnimating: Bool = false
    @State private var showQuickActions: Bool = false
    @State private var showChatMessages: Bool = false
    @State private var isTyping: Bool = false
    @State private var scrollOffset: CGFloat = 0

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                backgroundLayer

                // Main content
                VStack(spacing: 0) {
                    // Main content area
                    ZStack {
                        // Central orb (only show when not in chat mode)
                        if !showChatMessages {
                            centralOrbLayer
                        }

                        // Chat messages (only show when in chat mode)
                        if showChatMessages && !messages.isEmpty {
                            chatMessagesLayer
                        }

                        // Quick action buttons (only show when not in chat mode)
                        if !showChatMessages {
                            quickActionsLayer
                        }
                    }

                    Spacer()

                    // Bottom input area - always visible
                    bottomInputLayer
                }

                // Top navigation overlay with glassmorphic effect
                topNavigationOverlay
            }
        }
        .frame(width: DesignSystem.Dimensions.screenWidth, height: DesignSystem.Dimensions.screenHeight)
        .background(DesignSystem.Colors.backgroundPrimary)
        .ignoresSafeArea(.all)
        .onAppear {
            startOrbAnimation()
            showQuickActionsWithDelay()
        }
    }
}

// MARK: - View Layers

extension ChatView {

    private var backgroundLayer: some View {
        DesignSystemComponents.AppBackground()
    }

    private var topNavigationOverlay: some View {
        VStack(spacing: 0) {
            DesignSystemComponents.GlassmorphicNavigationBar(
                isVisible: showChatMessages && scrollOffset > 20,
                opacity: min(scrollOffset / 100, 0.9)
            ) {
                HStack {
                    // Back button with menu icon
                    DesignSystemComponents.CircularIconButton(icon: "line.3.horizontal") {
                        resetToInitialState()
                    }

                    Spacer()

                    // New chat button
                    DesignSystemComponents.CircularIconButton(icon: "square.and.pencil") {
                        // Handle new chat action
                        resetToInitialState()
                    }

                    // Right side menu button (filled)
                    DesignSystemComponents.CircularIconButton(icon: "line.3.horizontal", isFilled: true) {
                        // Handle help action
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.lg)
                .padding(.bottom, DesignSystem.Spacing.lg)
            }
            .safeAreaInset(edge: .top) {
                Color.clear.frame(height: 0)
            }

            Spacer()
        }
    }

    private var centralOrbLayer: some View {
        VStack {
            Spacer()

            DesignSystemComponents.AnimatedOrb(isAnimating: $isOrbAnimating) {
                startConversation()
            }

            Spacer()
        }
    }

    private var chatMessagesLayer: some View {
        ScrollViewReader { proxy in
            ScrollView {
                GeometryReader { geometry in
                    Color.clear.preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
                }
                .frame(height: 0)

                LazyVStack(spacing: DesignSystem.Spacing.lg) {
                    ForEach(messages) { message in
                        DesignSystemComponents.MessageBubble(
                            message: message.content,
                            isFromUser: message.isFromUser
                        )
                        .id(message.id)
                    }

                    if isTyping {
                        TypingIndicator()
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, 100) // Increased to account for safe area
                .padding(.bottom, 200)
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = -value
            }
            .onChange(of: messages.count) { _, _ in
                if let lastMessage = messages.last {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    private var quickActionsLayer: some View {
        VStack {
            Spacer()

            VStack(spacing: DesignSystem.Spacing.lg) {
                // Upload receipt button
                DesignSystemComponents.QuickActionButton(title: "Nahrát účtenku/fakturu") {
                    handleQuickAction("Nahrát účtenku/fakturu")
                }
                .opacity(showQuickActions ? 1.0 : 0.0)
                .offset(y: showQuickActions ? 0 : 20)
                .animation(DesignSystem.Animation.springMedium.delay(0.3), value: showQuickActions)

                // Building problem button
                DesignSystemComponents.QuickActionButton(title: "Problém v budově") {
                    handleQuickAction("Problém v budově")
                }
                .opacity(showQuickActions ? 1.0 : 0.0)
                .offset(y: showQuickActions ? 0 : 20)
                .animation(DesignSystem.Animation.springMedium.delay(0.5), value: showQuickActions)
            }
            .padding(.bottom, 180)

            Spacer()
        }
    }

    private var bottomInputLayer: some View {
        VStack(spacing: 0) {
            // Input area with glassmorphic background
            DesignSystemComponents.GlassmorphicContainer(
                cornerRadius: DesignSystem.CornerRadius.md
            ) {
                HStack(spacing: DesignSystem.Spacing.lg) {
                    // Left button (plus circle or microphone)
                    DesignSystemComponents.CircularIconButton(icon: "plus.circle") {
                        handleVoiceInput()
                    }

                    // Text input
                    DesignSystemComponents.GlassmorphicTextField(
                        text: $messageText,
                        placeholder: "Napište..."
                    ) {
                        sendMessage()
                    }

                    // Send button
                    DesignSystemComponents.CircularIconButton(icon: "paperplane.fill", isFilled: true) {
                        if messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            handleVoiceInput()
                        } else {
                            sendMessage()
                        }
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.lg)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.sm)

            // Home indicator area with iPhone-style bezels
            DesignSystemComponents.HomeIndicator()
        }
    }


}

// MARK: - Helper Methods

extension ChatView {

    private func startOrbAnimation() {
        withAnimation(DesignSystem.Animation.easeInOutFast.delay(0.5)) {
            isOrbAnimating = true
        }
    }

    private func showQuickActionsWithDelay() {
        withAnimation(DesignSystem.Animation.springMedium.delay(1.0)) {
            showQuickActions = true
        }
    }

    private func startConversation() {
        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = true
        }

        // Add welcome message
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            addMessage("Ahoj! Jak vám mohu pomoci?", isFromUser: false)
        }
    }

    private func handleQuickAction(_ action: String) {
        addMessage(action, isFromUser: true)

        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = true
        }

        // Simulate AI response
        simulateAIResponse(for: action)
    }

    private func sendMessage() {
        let trimmedMessage = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedMessage.isEmpty else { return }

        addMessage(trimmedMessage, isFromUser: true)
        messageText = ""

        if !showChatMessages {
            withAnimation(DesignSystem.Animation.springMedium) {
                showChatMessages = true
            }
        }

        // Simulate AI response
        simulateAIResponse(for: trimmedMessage)
    }

    private func addMessage(_ content: String, isFromUser: Bool) {
        let message = ChatMessage(content: content, isFromUser: isFromUser)
        withAnimation(DesignSystem.Animation.springFast) {
            messages.append(message)
        }
    }

    private func simulateAIResponse(for userMessage: String) {
        isTyping = true

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isTyping = false

            let responses = [
                "Rozumím vašemu dotazu. Jak vám mohu dále pomoci?",
                "Děkuji za informaci. Potřebujete něco dalšího?",
                "To je zajímavé. Můžete mi říct více detailů?",
                "Určitě vám s tím pomohu. Jaké máte další otázky?"
            ]

            let response = responses.randomElement() ?? responses[0]
            addMessage(response, isFromUser: false)
        }
    }

    private func handleVoiceInput() {
        // Handle voice input functionality
        print("Voice input triggered")
    }

    private func resetToInitialState() {
        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = false
            messages.removeAll()
            messageText = ""
            isTyping = false
        }
    }
}

// MARK: - Typing Indicator

struct TypingIndicator: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack {
            HStack(spacing: DesignSystem.Spacing.xs) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(DesignSystem.Colors.textTertiary)
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            DesignSystem.Animation.typingDot.delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.messageBubble)
                    .fill(DesignSystem.Colors.surfacePrimary)
            )

            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

// MARK: - Chat Message Model

struct ChatMessage: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date

    init(content: String, isFromUser: Bool) {
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = Date()
    }
}

// MARK: - Scroll Offset Preference Key

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0

    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Preview

#Preview {
    ChatView()
}